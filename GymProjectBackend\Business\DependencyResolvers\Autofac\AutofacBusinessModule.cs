﻿using Autofac;
using Autofac.Extras.DynamicProxy;
using Business.Abstract;
using Business.Concrete;
using Castle.DynamicProxy;
using Core.Utilities.Interceptors;
using Core.Utilities.Security.JWT;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.DependencyResolvers.Autofac
{
    public class AutofacBusinessModule:Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            // ⚠️ KRİTİK DÜZELTİLDİ: Tüm servisler InstancePerLifetimeScope() olarak değiştirildi
            // Multi-tenant ortamda veri karışmasını önlemek için gerekli

            builder.RegisterType<UserManager>().As<IUserService>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserDal>().As<IUserDal>().InstancePerLifetimeScope();
            builder.RegisterType<CompanyManager>().As<ICompanyService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyDal>().As<ICompanyDal>().InstancePerLifetimeScope();
            builder.RegisterType<CompanyAdressManager>().As<ICompanyAdressService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyAdressDal>().As<ICompanyAdressDal>().InstancePerLifetimeScope();
            builder.RegisterType<MemberManager>().As<IMemberService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMemberDal>().As<IMemberDal>().InstancePerLifetimeScope();
            builder.RegisterType<MembershipTypeManager>().As<IMembershipTypeService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMembershipTypeDal>().As<IMembershiptypeDal>().InstancePerLifetimeScope();
            builder.RegisterType<MembershipManager>().As<IMembershipService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMembershipDal>().As<IMembershipDal>().InstancePerLifetimeScope();
            builder.RegisterType<CompanyUserManager>().As<ICompanyUserService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyUserDal>().As<ICompanyUserDal>().InstancePerLifetimeScope();
            builder.RegisterType<PaymentManager>().As<IPaymentService>().InstancePerLifetimeScope();
            builder.RegisterType<EfPaymentDal>().As<IPaymentDal>().InstancePerLifetimeScope();
            builder.RegisterType<UserCompanyManager>().As<IUserCompanyService>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserCompanyDal>().As<IUserCompanyDal>().InstancePerLifetimeScope();
            builder.RegisterType<UnifiedCompanyManager>().As<IUnifiedCompanyService>().InstancePerLifetimeScope();
            builder.RegisterType<EntryExitHistoryManager>().As<IEntryExitHistoryService>().InstancePerLifetimeScope();
            builder.RegisterType<EfEntryExitHistoryDal>().As<IEntryExitHistoryDal>().InstancePerLifetimeScope();
            builder.RegisterType<CityManager>().As<ICityService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCityDal>().As<ICityDal>().InstancePerLifetimeScope();
            builder.RegisterType<TownManager>().As<ITownService>().InstancePerLifetimeScope();
            builder.RegisterType<EfTownDal>().As<ITownDal>().InstancePerLifetimeScope();

            // ⚠️ STATELESS SERVİSLER: Bu servisler state tutmadığı için InstancePerLifetimeScope yeterli
            builder.RegisterType<AuthManager>().As<IAuthService>().InstancePerLifetimeScope();
            builder.RegisterType<JwtHelper>().As<ITokenHelper>().InstancePerLifetimeScope();

            builder.RegisterType<ProductManager>().As<IProductService>().InstancePerLifetimeScope();
            builder.RegisterType<EfProductDal>().As<IProductDal>().InstancePerLifetimeScope();
            builder.RegisterType<TransactionManager>().As<ITransactionService>().InstancePerLifetimeScope();
            builder.RegisterType<EfTransactionDal>().As<ITransactionDal>().InstancePerLifetimeScope();
            builder.RegisterType<OperationClaimManager>().As<IOperationClaimService>().InstancePerLifetimeScope();
            builder.RegisterType<EfOperationClaimDal>().As<IOperationClaimDal>().InstancePerLifetimeScope();
            builder.RegisterType<UserOperationClaimManager>().As<IUserOperationClaimService>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserOperationClaimDal>().As<IUserOperationClaimDal>().InstancePerLifetimeScope();
            builder.RegisterType<RemainingDebtManager>().As<IRemainingDebtService>().InstancePerLifetimeScope();
            builder.RegisterType<EfRemainingDebtDal>().As<IRemainingDebtDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfDebtPaymentDal>().As<IDebtPaymentDal>().InstancePerLifetimeScope();
            builder.RegisterType<DebtPaymentManager>().As<IDebtPaymentService>().InstancePerLifetimeScope();

            // ⚠️ SINGLETON KALACAKLAR: HttpContextAccessor gerçekten singleton olmalı
            builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().SingleInstance();

            // Multi-tenant Company Context
            builder.RegisterType<CompanyContext>().As<ICompanyContext>().InstancePerLifetimeScope();

            builder.RegisterType<UserDeviceManager>().As<IUserDeviceService>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserDeviceDal>().As<IUserDeviceDal>().InstancePerLifetimeScope();
            builder.RegisterType<MembershipFreezeHistoryManager>().As<IMembershipFreezeHistoryService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMembershipFreezeHistoryDal>().As<IMembershipFreezeHistoryDal>().InstancePerLifetimeScope();
            builder.RegisterType<LicensePackageManager>().As<ILicensePackageService>().InstancePerLifetimeScope();
            builder.RegisterType<EfLicensePackageDal>().As<ILicensePackageDal>().InstancePerLifetimeScope();
            builder.RegisterType<UserLicenseManager>().As<IUserLicenseService>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserLicenseDal>().As<IUserLicenseDal>().InstancePerLifetimeScope();
            builder.RegisterType<LicenseTransactionManager>().As<ILicenseTransactionService>().InstancePerLifetimeScope();
            builder.RegisterType<EfLicenseTransactionDal>().As<ILicenseTransactionDal>().InstancePerLifetimeScope();

            // Expense Service ve Dal Kayıtları
            builder.RegisterType<ExpenseManager>().As<IExpenseService>().InstancePerLifetimeScope();
            builder.RegisterType<EfExpenseDal>().As<IExpenseDal>().InstancePerLifetimeScope();

            // File Service Kaydı
            builder.RegisterType<FileManager>().As<IFileService>().InstancePerLifetimeScope();

            // Profile Service Kaydı
            builder.RegisterType<ProfileManager>().As<IProfileService>().InstancePerLifetimeScope();

            // Advanced Rate Limit Service Kaydı
            builder.RegisterType<AdvancedRateLimitManager>().As<IAdvancedRateLimitService>().InstancePerLifetimeScope();

            // QR Code Encryption Service Kaydı
            builder.RegisterType<QrCodeEncryptionManager>().As<IQrCodeEncryptionService>().SingleInstance();

            // Cache Service Kaydı
            builder.RegisterType<CacheManager>().As<ICacheService>().SingleInstance();

            // Egzersiz Sistemi Service ve Dal Kayıtları
            builder.RegisterType<ExerciseCategoryManager>().As<IExerciseCategoryService>().InstancePerLifetimeScope();
            builder.RegisterType<EfExerciseCategoryDal>().As<IExerciseCategoryDal>().InstancePerLifetimeScope();
            builder.RegisterType<SystemExerciseManager>().As<ISystemExerciseService>().InstancePerLifetimeScope();
            builder.RegisterType<EfSystemExerciseDal>().As<ISystemExerciseDal>().InstancePerLifetimeScope();
            builder.RegisterType<CompanyExerciseManager>().As<ICompanyExerciseService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyExerciseDal>().As<ICompanyExerciseDal>().InstancePerLifetimeScope();

            // Antrenman Programı Sistemi
            builder.RegisterType<WorkoutProgramTemplateManager>().As<IWorkoutProgramTemplateService>().InstancePerLifetimeScope();
            builder.RegisterType<EfWorkoutProgramTemplateDal>().As<IWorkoutProgramTemplateDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfWorkoutProgramDayDal>().As<IWorkoutProgramDayDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfWorkoutProgramExerciseDal>().As<IWorkoutProgramExerciseDal>().InstancePerLifetimeScope();

            // Üye Program Atama Sistemi
            builder.RegisterType<MemberWorkoutProgramManager>().As<IMemberWorkoutProgramService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMemberWorkoutProgramDal>().As<IMemberWorkoutProgramDal>().InstancePerLifetimeScope();

            // ⚠️ ASPECT INTERCEPTOR: Bu da InstancePerLifetimeScope olmalı
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            builder.RegisterAssemblyTypes(assembly).AsImplementedInterfaces()
                .EnableInterfaceInterceptors(new ProxyGenerationOptions()
                {
                    Selector = new AspectInterceptorSelector()
                }).InstancePerLifetimeScope();
        }
    }
}
