﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyDal : EfEntityRepositoryBase<Company, GymContext>, ICompanyDal
    {
        // ⚠️ KRİTİK EKLENDİ: Constructor injection ile GymContext alınıyor
        public EfCompanyDal(GymContext context) : base(context)
        {
        }

        public List<ActiveCompanyDetailDto> GetActiveCompanies()
        {
            // ⚠️ KRİTİK DÜZELTİLDİ: "using new" yerine injected context kullanılıyor
            var result = from c in _context.Companies
                         where c.IsActive == true
                         select new ActiveCompanyDetailDto
                         {
                           CompanyID = c.CompanyID,
                           CompanyName = c.CompanyName,
                           PhoneNumber = c.PhoneNumber,
                           IsActive = c.<PERSON>,

                         };
            return result.ToList();
        }
    }
}
