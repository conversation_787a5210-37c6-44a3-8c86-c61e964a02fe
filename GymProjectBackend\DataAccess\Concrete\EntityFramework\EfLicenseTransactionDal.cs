using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfLicenseTransactionDal : EfEntityRepositoryBase<LicenseTransaction, GymContext>, ILicenseTransactionDal
    {
        // ⚠️ KRİTİK EKLENDİ: Constructor injection ile GymContext alınıyor
        public EfLicenseTransactionDal(GymContext context) : base(context)
        {
        }
    }

}
