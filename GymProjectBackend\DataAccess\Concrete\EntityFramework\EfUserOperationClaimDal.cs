using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserOperationClaimDal : EfEntityRepositoryBase<UserOperationClaim, GymContext>, IUserOperationClaimDal
    {
        // ⚠️ KRİTİK EKLENDİ: Constructor injection ile GymContext alınıyor
        public EfUserOperationClaimDal(GymContext context) : base(context)
        {
        }

        public List<UserOperationClaimDto> GetUserOperationClaimDetails()
        {
            // ⚠️ KRİTİK DÜZELTİLDİ: "using new" yerine injected context kullanılıyor
            {
                var result = from uoc in context.UserOperationClaims
                             join u in context.Users on uoc.UserId equals u.UserID
                             join oc in context.OperationClaims on uoc.OperationClaimId equals oc.OperationClaimId
                             select new UserOperationClaimDto
                             {
                                 UserOperationClaimId = uoc.UserOperationClaimId,
                                 UserId = uoc.UserId,
                                 UserName = u.FirstName + " " + u.LastName,
                                 OperationClaimId = uoc.OperationClaimId,
                                 OperationClaimName = oc.Name,
                                 Email=u.Email
                             };
                return result.ToList();
            }
        }
    }
}
